<template>
  <view class="tutorial-container">
    <!-- 顶部导航 -->
    <view class="header" :style="{ paddingTop: statusBarHeight + 'px', height: customNavHeight + 'px' }">
      <view class="header-content">
        <view class="back-btn" @click="goBack">
          <view class="back-btn-inner">
            <text class="back-icon">‹</text>
            <text class="back-text">返回</text>
          </view>
        </view>
        <text class="title">使用教程</text>
        <view class="placeholder"></view>
      </view>
    </view>

    <!-- 左侧导航栏 -->
    <view class="sidebar" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
      <view class="sidebar-header">
        <text v-if="!sidebarCollapsed" class="sidebar-title">📋 目录</text>
      </view>
      <scroll-view v-if="!sidebarCollapsed" class="sidebar-content" scroll-y="true">
        <view
          class="sidebar-item"
          v-for="(item, index) in catalogItems"
          :key="index"
          :class="{ 'sidebar-item-active': currentSection === item.id }"
          @click="scrollToSection(item.id)"
        >
          <text class="sidebar-icon">{{ item.icon }}</text>
          <text class="sidebar-text">{{ item.title }}</text>
        </view>

        <!-- 侧边栏广告位 -->
        <view v-if="shouldShowSidebarAd" class="sidebar-ad-container">
          <view class="sidebar-ad-content">
            <text class="sidebar-ad-title">💡 小贴士</text>
            <text class="sidebar-ad-text">分享给好友可获得24小时无限解析权限哦！</text>
            <view class="sidebar-ad-button" @click="handleSidebarAdClick">
              <text class="sidebar-ad-button-text">分享好友</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 浮动切换按钮 -->
    <view class="floating-toggle" @click="toggleSidebar">
      <text class="floating-toggle-text">{{ sidebarCollapsed ? '目录' : '×' }}</text>
    </view>

    <!-- 主内容区域 -->
    <view class="main-content" :class="{ 'main-content-expanded': sidebarCollapsed }">
      <scroll-view class="content-scroll" scroll-y="true" enhanced="true" show-scrollbar="false">
        <!-- 顶部广告位 -->
        <view v-if="shouldShowTopAd" class="tutorial-ad-placeholder">
          <view class="tutorial-ad-header">
            <text class="tutorial-ad-label">📺 广告</text>
          </view>
          <view class="tutorial-ad-content">
            <view class="tutorial-ad-thumbnail">
              <text class="tutorial-ad-icon">📚</text>
            </view>
            <view class="tutorial-ad-info">
              <text class="tutorial-ad-title">高效学习工具</text>
              <text class="tutorial-ad-desc">专业的在线学习平台，提升技能更轻松</text>
              <view class="tutorial-ad-source">
                <text class="tutorial-ad-source-icon">🎓</text>
                <text class="tutorial-ad-source-text">学习助手</text>
              </view>
            </view>
            <button class="tutorial-ad-action-btn" @click="handleTopAdClick">了解更多</button>
          </view>
        </view>

        <!-- 教程章节 -->
        <view class="sections">
        <!-- 第一章：基础使用 -->
        <view class="section" id="basic-usage">
          <view class="section-header">
            <text class="section-icon">🚀</text>
            <text class="section-title">基础使用教程</text>
          </view>
          
          <view class="step-card">
            <text class="step-title">步骤 1：找到分享按钮</text>
            <text class="step-desc">在视频/音乐平台找到想要去水印的视频，点击分享按钮（可能是箭头图标或三个点图标）</text>
            <view class="tutorial-image-container">
              <image class="tutorial-image" src="/static/tutorial/share-button-1.jpg" mode="aspectFit" @error="handleImageError" />
              <text class="image-caption">分享按钮（箭头图标）</text>
            </view>
            <view class="tutorial-image-container">
              <image class="tutorial-image" src="/static/tutorial/share-button-2.jpg" mode="aspectFit" @error="handleImageError" />
              <text class="image-caption">分享按钮（三个点图标）</text>
            </view>
          </view>

          <view class="step-card">
            <text class="step-title">步骤 2：复制视频链接</text>
            <text class="step-desc">在分享菜单中找到"复制链接"选项并点击</text>
            <view class="tutorial-image-container">
              <image class="tutorial-image" src="/static/tutorial/copy-link.jpg" mode="aspectFit" @error="handleImageError" />
              <text class="image-caption">点击"复制链接"</text>
            </view>
          </view>

          <view class="step-card">
            <text class="step-title">步骤 3：粘贴到工具</text>
            <text class="step-desc">打开本工具，将复制的链接粘贴到输入框中，或开启"自动粘贴"功能</text>
            <view class="tutorial-image-container">
              <image class="tutorial-image" src="/static/tutorial/paste-link.jpg" mode="aspectFit" @error="handleImageError" />
              <text class="image-caption">粘贴链接到输入框</text>
            </view>
          </view>

          <view class="step-card">
            <text class="step-title">步骤 4：一键解析</text>
            <text class="step-desc">点击"Get无水印"按钮，等待系统自动解析处理</text>
            <view class="tutorial-image-container">
              <image class="tutorial-image" src="/static/tutorial/get-video.jpg" mode="aspectFit" @error="handleImageError" />
              <text class="image-caption">点击"Get无水印"按钮</text>
            </view>
          </view>

          <view class="step-card">
            <text class="step-title">步骤 5：保存到相册</text>
            <text class="step-desc">解析完成后，点击"保存到相册"即可将无水印视频保存到手机。保存成功后会显示具体的保存路径。</text>
            <view class="tutorial-image-container">
              <image class="tutorial-image" src="/static/tutorial/save-to-album.jpg" mode="aspectFit" @error="handleImageError" />
              <text class="image-caption">保存到相册及保存路径</text>
            </view>
            <view class="save-path-info">
              <text class="save-path-title">📁 保存位置说明：</text>
              <text class="save-path-desc">• 通常保存在：手机相册 > 相机 > 图片</text>
              <text class="save-path-desc">• 具体路径：/storage/emulated/0/DCIM/Camera</text>
              <text class="save-path-desc">• 部分手机（如OPPO）需要到"文件管理"中搜索"mp4"文件</text>
              <text class="save-path-desc">• 保存成功后界面会显示具体的存储路径</text>
            </view>
          </view>
        </view>

        <!-- 第二章：支持平台 -->
        <view class="section" id="supported-platforms">
          <view class="section-header">
            <text class="section-icon">🌐</text>
            <text class="section-title">支持的平台</text>
          </view>
          
          <view class="platform-grid">
            <view class="platform-item">
              <text class="platform-icon">🎵</text>
              <text class="platform-name">抖音</text>
              <text class="platform-desc">支持视频、图文</text>
            </view>
            <view class="platform-item">
              <text class="platform-icon">⚡</text>
              <text class="platform-name">快手</text>
              <text class="platform-desc">支持视频、图文</text>
            </view>
            <view class="platform-item">
              <text class="platform-icon">📖</text>
              <text class="platform-name">小红薯</text>
              <text class="platform-desc">支持图文、视频、Live Photo、纯文案</text>
            </view>
            <view class="platform-item">
              <text class="platform-icon">📺</text>
              <text class="platform-name">哔哩哔哩</text>
              <text class="platform-desc">支持视频、图文、Live Photo、专栏、纯文案</text>
            </view>
            <view class="platform-item">
              <text class="platform-icon">🧣</text>
              <text class="platform-name">围脖</text>
              <text class="platform-desc">支持视频、图文、Live Photo、纯文案</text>
            </view>
            <view class="platform-item">
              <text class="platform-icon">🎭</text>
              <text class="platform-name">微视</text>
              <text class="platform-desc">支持短视频</text>
            </view>
            <view class="platform-item">
              <text class="platform-icon">🦐</text>
              <text class="platform-name">皮皮虾</text>
              <text class="platform-desc">支持视频、图文、纯文案</text>
            </view>
            <view class="platform-item">
              <text class="platform-icon">🎶</text>
              <text class="platform-name">汽水音乐</text>
              <text class="platform-desc">支持音乐视频</text>
            </view>
          </view>
        </view>

        <!-- 第三章：常见问题 -->
        <view class="section" id="faq">
          <view class="section-header">
            <text class="section-icon">❓</text>
            <text class="section-title">常见问题解答</text>
          </view>

          <view class="faq-list">
            <view class="faq-item">
              <text class="faq-question">Q: 为什么会解析失败？</text>
              <view class="faq-answer-list">
                <text class="faq-answer-item">1. 刚发布的视频可能还在平台审核中，建议稍后重试</text>
                <text class="faq-answer-item">2. 视频设置为私密或好友可见，需要设置为公开状态</text>
                <text class="faq-answer-item">3. 原作者可能已删除该视频，可复制链接到浏览器确认</text>
                <text class="faq-answer-item">4. 视频来自暂不支持的平台</text>
              </view>
            </view>

            <view class="faq-item">
              <text class="faq-question">Q: 为什么保存了还有水印？</text>
              <view class="faq-answer-list">
                <text class="faq-answer-item">1. 视频作者发布时本身就带有水印，无法在线去除</text>
                <text class="faq-answer-item">2. 部分平台仅支持下载，不支持去水印功能</text>
              </view>
            </view>

            <view class="faq-item">
              <text class="faq-question">Q: 为什么下载失败？</text>
              <view class="faq-answer-list">
                <text class="faq-answer-item">1. 视频超过200MB，微信限制文件大小，可点击"复制链接"到浏览器下载</text>
                <text class="faq-answer-item">2. 网络连接不稳定，建议在WiFi环境下重试</text>
                <text class="faq-answer-item">3. 手机存储空间不足，请清理后重试</text>
                <text class="faq-answer-item">4. 相册权限未开启，请在设置中允许访问相册</text>
              </view>
            </view>

            <view class="faq-item">
              <text class="faq-question">Q: 相册找不到刚保存的视频？</text>
              <view class="faq-answer-list">
                <text class="faq-answer-item">1. 部分手机（如OPPO）需要到"文件管理"中查找，搜索"mp4"文件</text>
                <text class="faq-answer-item">2. 保存成功后查看下方的保存位置，有具体存储路径</text>
                <text class="faq-answer-item">3. 尝试在微信中发送视频消息，看是否能选中下载的视频</text>
              </view>
            </view>

            <view class="faq-item">
              <text class="faq-question">Q: 如何保存大于200MB的视频？</text>
              <view class="faq-answer-list">
                <text class="faq-answer-item">1. 点击"复制链接"按钮获取无水印视频链接</text>
                <text class="faq-answer-item">2. 将链接粘贴到浏览器或者迅雷等App下载</text>
                <text class="faq-answer-item">3. 安卓建议使用默认浏览器，苹果手机需下载Documents应用</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 第四章：注意事项 -->
        <view class="section" id="notes">
          <view class="section-header">
            <text class="section-icon">⚠️</text>
            <text class="section-title">使用注意事项</text>
          </view>

          <view class="note-list">
            <view class="note-item warning">
              <text class="note-icon">🚫</text>
              <text class="note-text">请勿用于商业用途，仅供个人学习交流使用</text>
            </view>
            <view class="note-item info">
              <text class="note-icon">📝</text>
              <text class="note-text">版权归原作者所有，请尊重原创内容</text>
            </view>
            <view class="note-item tip">
              <text class="note-icon">💡</text>
              <text class="note-text">建议在WiFi环境下使用，避免消耗过多流量</text>
            </view>
            <view class="note-item success">
              <text class="note-icon">✅</text>
              <text class="note-text">工具完全免费，无需注册登录</text>
            </view>
          </view>
        </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import adManager from '@/components/ad-config.js'
import shareManager from '@/components/share-config.js'

export default {
  data() {
    return {
      sidebarCollapsed: true, // 默认关闭
      currentSection: 'basic-usage',
      statusBarHeight: 0,
      customNavHeight: 0,

      catalogItems: [
        { id: 'basic-usage', icon: '🚀', title: '基础使用教程' },
        { id: 'supported-platforms', icon: '🌐', title: '支持的平台' },
        { id: 'faq', icon: '❓', title: '常见问题解答' },
        { id: 'notes', icon: '⚠️', title: '使用注意事项' }
      ]
    }
  },

  computed: {
    // 是否显示侧边栏广告（改为受分享开关控制）
    shouldShowSidebarAd() {
      return shareManager.shouldShowShare('tutorialSidebar')
    },

    // 是否显示顶部广告（受广告总开关控制）
    shouldShowTopAd() {
      return adManager.shouldShowAd('tutorial-top')
    }
  },
  
  onLoad(options) {
    // 获取系统信息
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight || 0
    this.customNavHeight = this.statusBarHeight + 44 // 状态栏高度 + 导航栏高度

    // 如果有指定章节，自动滚动到对应位置
    if (options.section) {
      setTimeout(() => {
        this.scrollToSection(options.section)
      }, 500)
    }
  },
  
  methods: {
    goBack() {
      uni.navigateBack()
    },


    
    scrollToSection(sectionId) {
      this.currentSection = sectionId
      // 点击目录项后自动收缩导航栏
      this.sidebarCollapsed = true

      // 等待导航栏收缩动画完成后再滚动
      setTimeout(() => {
        // 创建选择器查询，查询整个页面的元素
        const query = uni.createSelectorQuery()

        // 同时查询目标元素和滚动容器
        query.select(`#${sectionId}`).boundingClientRect()
        query.select('.content-scroll').boundingClientRect()
        query.selectViewport().scrollOffset()

        query.exec((res) => {
          const targetRect = res[0]
          const scrollRect = res[1]
          const scrollOffset = res[2]

          if (targetRect && scrollRect) {
            console.log(`[滚动定位] 目标: ${sectionId}`)
            console.log(`[滚动定位] 目标位置:`, targetRect)
            console.log(`[滚动定位] 滚动容器:`, scrollRect)
            console.log(`[滚动定位] 当前滚动:`, scrollOffset)

            // 计算精确的滚动位置
            // 目标元素相对于页面顶部的位置 - 固定头部的高度
            const systemInfo = uni.getSystemInfoSync()
            const statusBarHeight = systemInfo.statusBarHeight || 44
            const navBarHeight = 44 // 导航栏实际高度(px)
            const extraOffset = 20 // 额外偏移，避免贴边

            // 计算最终滚动位置
            const finalScrollTop = targetRect.top + scrollOffset.scrollTop - statusBarHeight - navBarHeight - extraOffset

            console.log(`[滚动定位] 计算结果: ${finalScrollTop}`)

            // 执行滚动
            uni.pageScrollTo({
              scrollTop: Math.max(0, finalScrollTop), // 确保不会滚动到负数位置
              duration: 300
            })
          } else {
            console.error(`[滚动定位] 未找到目标元素: ${sectionId}`)
          }
        })
      }, 350) // 稍微增加等待时间
    },

    // 切换侧边栏收缩状态
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed
    },

    // 处理侧边栏分享按钮点击
    handleSidebarAdClick() {
      console.log('[教程页面] 侧边栏分享按钮被点击')

      // 检查是否为测试模式
      if (shareManager.config.testMode) {
        console.log('[教程页面] 测试模式，直接模拟分享成功')
        // 直接触发分享成功处理
        shareManager.handleShareSuccess({
          shareType: 'wechatFriend',
          source: 'tutorialSidebar'
        })
      } else {
        // 生产模式，触发真实分享
        uni.showShareMenu({
          withShareTicket: true,
          success: () => {
            shareManager.handleShareSuccess({
              shareType: 'wechatFriend',
              source: 'tutorialSidebar'
            })
          }
        })
      }
    },

    // 处理顶部广告点击
    handleTopAdClick() {
      console.log('[教程页面] 顶部广告被点击')

      // 显示广告点击提示
      uni.showToast({
        title: '广告点击成功',
        icon: 'success',
        duration: 1500
      })

      // 这里可以添加广告点击统计或跳转逻辑
      // 例如：跳转到广告主页面、记录点击数据等
    },

    // 处理图片加载错误
    handleImageError(e) {
      console.log('[教程页面] 图片加载失败:', e)
      // 可以在这里添加统计或其他处理逻辑
      // 由于已转换为JPG格式，理论上不应该再出现兼容性问题
    },

    // 微信分享回调
    onShareAppMessage(res) {
      console.log('[教程页面] 微信分享被触发', res)
      console.log('[分享] 分享触发来源:', res.from) // button（按钮触发）或 menu（右上角菜单触发）
      
      // 定义分享内容
      const shareInfo = {
        // 分享标题
        title: 'MarkEraser - 短视频去水印工具使用教程',
        
        // 分享描述
        desc: '免费去除抖音、快手、小红书、汽水音乐等平台视频水印，简单易用！',
        
        // 分享路径
        path: '/pages/tutorial/index',
        
        // 分享卡片的图片
        imageUrl: '/static/share-card.jpg',
        
        // 分享成功的回调
        success: (res) => {
          console.log('[教程页面] 分享成功', res)
          
          // 🎁 调用分享成功处理，获得24小时权限
          shareManager.handleShareSuccess({
            success: true,
            shareType: 'wechatFriend',
            source: 'tutorialPage',
            timestamp: Date.now()
          })
          
          // 显示分享成功提示（增强版）
          uni.showModal({
            title: '分享成功！',
            content: '恭喜您获得24小时免费解析权限！快去体验完整功能吧~',
            showCancel: false,
            confirmText: '去解析',
            success: () => {
              console.log('[教程页面] 用户确认24小时权限提示')
              // 跳转到首页
              uni.switchTab({
                url: '/pages/watermark-remover/index'
              })
            }
          })
        },
        
        // 分享失败的回调（包括用户取消分享）
        fail: (res) => {
          console.log('[教程页面] 分享失败', res)
          
          // 如果是用户取消，不提示；其他错误才提示
          if (res.errMsg !== 'shareAppMessage:fail cancel') {
            uni.showToast({
              title: '分享失败，请重试',
              icon: 'none',
              duration: 2000
            })
          } else {
            console.log('[教程页面] 用户取消分享')
          }
        }
      }
      
      return shareInfo
    }
  }
}
</script>

<style scoped>
.tutorial-container {
  min-height: 100vh;
  background: #F8F9FA;
  display: flex;
  flex-direction: column;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #E1251B 0%, #FF4142 100%);
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  height: 88rpx;
}

.back-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  min-width: 120rpx;
  justify-content: center;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.back-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.back-btn-inner {
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 36rpx;
  color: #FFFFFF;
  margin-right: 8rpx;
  font-weight: 600;
  line-height: 1;
}

.back-text {
  font-size: 28rpx;
  color: #FFFFFF;
  font-weight: 500;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
}

.placeholder {
  width: 120rpx;
}

/* 左侧导航栏样式 */
.sidebar {
  position: fixed;
  left: 0;
  top: 160rpx; /* 增加顶部间距，确保不被头部遮挡 */
  bottom: 0;
  width: 280rpx; /* 进一步减小宽度 */
  background: #ffffff;
  border-right: 2rpx solid #E5E5E5;
  z-index: 500; /* 确保在头部下面 */
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.sidebar-collapsed {
  width: 0rpx; /* 完全隐藏 */
  border-right: none;
  overflow: hidden; /* 确保内容完全隐藏 */
  opacity: 0; /* 完全透明 */
  left: -300rpx; /* 移出屏幕外 */
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border-bottom: 2rpx solid #F0F0F0;
  background: #F8F9FA;
  flex-shrink: 0;
}

.sidebar-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 浮动切换按钮 */
.floating-toggle {
  position: fixed;
  right: 20rpx;
  top: 180rpx; /* 调整到头部下方，与微信胶囊按钮保持合适间距 */
  padding: 16rpx 24rpx;
  background: #1976D2;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.3);
  transition: all 0.3s ease;
}

.floating-toggle:active {
  transform: scale(0.95);
  background: #1565C0;
}

.floating-toggle-text {
  font-size: 24rpx;
  color: #FFFFFF;
  font-weight: bold;
}

.sidebar-content {
  flex: 1;
  padding: 10rpx 0;
}

.sidebar-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  border-bottom: 1rpx solid #F5F5F5;
  transition: all 0.3s ease;
  cursor: pointer;
}

.sidebar-item:hover {
  background: #F8F9FA;
}

.sidebar-item:active {
  background: #E3F2FD;
}

.sidebar-item-active {
  background: #E3F2FD;
  border-right: 6rpx solid #E1251B;
}

.sidebar-icon {
  font-size: 28rpx;
  width: 40rpx;
  text-align: center;
  flex-shrink: 0;
}

.sidebar-text {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
}

.sidebar-item-active .sidebar-text {
  color: #E1251B;
  font-weight: 600;
}

/* 主内容区域样式 */
.main-content {
  flex: 1;
  margin-left: 280rpx; /* 调整为新的导航栏宽度 */
  margin-top: 132rpx; /* 为固定头部留出空间 (44px状态栏 + 44px导航栏) */
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.main-content-expanded {
  margin-left: 0rpx; /* 导航栏完全隐藏时无边距 */
}

.content-scroll {
  flex: 1;
  padding: 30rpx;
}

/* 教程页广告位样式 - 与结果页保持一致 */
.tutorial-ad-placeholder {
  background: #FFFFFF;
  margin: 20rpx 0;  /* 简化margin，避免复杂计算 */
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid #E0E0E0;
  width: 100%;
  box-sizing: border-box;
}

.tutorial-ad-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.tutorial-ad-label {
  font-size: 20rpx;
  color: #999;
  background: #F5F5F5;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.tutorial-ad-content {
  display: flex;
  align-items: center;
  gap: 16rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
  padding: 20rpx;
  min-height: 80rpx;
  overflow: hidden;  /* 防止内容溢出 */
}

.tutorial-ad-thumbnail {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #E1251B 0%, #FF4142 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.tutorial-ad-icon {
  font-size: 32rpx;
  color: #FFFFFF;
}

.tutorial-ad-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tutorial-ad-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.tutorial-ad-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

.tutorial-ad-source {
  display: flex;
  align-items: center;
  gap: 4rpx;
  margin-top: 4rpx;
}

.tutorial-ad-source-icon {
  font-size: 16rpx;
  color: #999;
}

.tutorial-ad-source-text {
  font-size: 18rpx;
  color: #999;
}

.tutorial-ad-action-btn {
  background: linear-gradient(135deg, #E1251B 0%, #FF4142 100%);
  color: #FFFFFF;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 16rpx;
  font-size: 20rpx;
  font-weight: 600;
  flex-shrink: 0;
  min-width: 120rpx;
  max-width: 140rpx;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.tutorial-ad-action-btn:active {
  background: linear-gradient(135deg, #C41E3A 0%, #E1251B 100%);
  transform: scale(0.98);
}

/* 章节样式 */
.sections {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #F0F0F0;
}

.section-icon {
  font-size: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 步骤卡片样式 */
.step-card {
  background: #F8F9FA;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  border-left: 6rpx solid #E1251B;
}

.step-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #E1251B;
  margin-bottom: 12rpx;
  display: block;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
  display: block;
}

/* 教程图片样式 */
.tutorial-image-container {
  margin: 16rpx 0;
  text-align: center;
}

.tutorial-image {
  width: 100%;
  max-width: 600rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid #E0E0E0;
}

.image-caption {
  font-size: 22rpx;
  color: #666;
  margin-top: 8rpx;
  display: block;
  font-style: italic;
}

/* 保存路径信息样式 */
.save-path-info {
  background: #F0F8FF;
  border: 1rpx solid #B0D4F1;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-top: 16rpx;
}

.save-path-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #1976D2;
  display: block;
  margin-bottom: 12rpx;
}

.save-path-desc {
  font-size: 24rpx;
  color: #555;
  line-height: 1.5;
  display: block;
  margin-bottom: 8rpx;
}

.save-path-desc:last-child {
  margin-bottom: 0;
}

/* 保留原有的占位符样式作为备用 */
.image-placeholder {
  background: #E9ECEF;
  border: 2rpx dashed #CED4DA;
  border-radius: 8rpx;
  padding: 40rpx;
  text-align: center;
}

.placeholder-text {
  font-size: 24rpx;
  color: #6C757D;
  display: block;
  margin-bottom: 8rpx;
}

.placeholder-note {
  font-size: 20rpx;
  color: #ADB5BD;
}

/* 平台网格样式 */
.platform-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 0 20rpx; /* 添加左右内边距，避免贴边 */
}

.platform-item {
  background: #F8F9FA;
  border-radius: 12rpx;
  padding: 24rpx;
  text-align: center;
  border: 2rpx solid #E9ECEF;
  transition: all 0.3s ease;
}

.platform-item:active {
  background: #E3F2FD;
  border-color: #2196F3;
  transform: scale(0.95);
}

.platform-icon {
  font-size: 40rpx;
  display: block;
  margin-bottom: 12rpx;
}

.platform-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.platform-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

/* FAQ样式 */
.faq-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.faq-item {
  background: #F8F9FA;
  border-radius: 12rpx;
  padding: 24rpx;
  border-left: 6rpx solid #17A2B8;
}

.faq-question {
  font-size: 28rpx;
  font-weight: 600;
  color: #17A2B8;
  margin-bottom: 12rpx;
  display: block;
}

.faq-answer {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.faq-answer-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-top: 12rpx;
}

.faq-answer-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  padding-left: 20rpx;
  position: relative;
}

.faq-answer-item::before {
  content: "•";
  position: absolute;
  left: 0;
  color: #17A2B8;
  font-weight: bold;
}

/* 解决方案样式 */
.solution-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.solution-item {
  background: #F8F9FA;
  border-radius: 12rpx;
  padding: 24rpx;
  border-left: 6rpx solid #FFC107;
}

.solution-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #F57C00;
  margin-bottom: 12rpx;
  display: block;
}

.solution-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

.solution-steps {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.solution-step {
  font-size: 24rpx;
  color: #666;
  padding-left: 20rpx;
  position: relative;
}

.solution-step::before {
  content: "•";
  position: absolute;
  left: 0;
  color: #FFC107;
  font-weight: bold;
}

/* 注意事项样式 */
.note-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.note-item {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  padding: 20rpx;
  border-radius: 12rpx;
}

.note-item.warning {
  background: #FFF3E0;
  border-left: 6rpx solid #FF9800;
}

.note-item.info {
  background: #E3F2FD;
  border-left: 6rpx solid #2196F3;
}

.note-item.tip {
  background: #F3E5F5;
  border-left: 6rpx solid #9C27B0;
}

.note-item.success {
  background: #E8F5E8;
  border-left: 6rpx solid #4CAF50;
}

.note-icon {
  font-size: 24rpx;
  flex-shrink: 0;
  margin-top: 2rpx;
}

.note-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

/* 底部样式 */
.footer {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-top: 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.footer-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.footer-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.contact-item {
  font-size: 24rpx;
  color: #666;
}

/* 侧边栏广告样式 */
.sidebar-ad-container {
  margin-top: 40rpx;
  padding: 0 20rpx 20rpx;
}

.sidebar-ad-content {
  background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.2);
}

.sidebar-ad-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #FFFFFF;
  display: block;
  margin-bottom: 12rpx;
}

.sidebar-ad-text {
  font-size: 22rpx;
  color: #FFFFFF;
  line-height: 1.4;
  display: block;
  margin-bottom: 16rpx;
  opacity: 0.9;
}

.sidebar-ad-button {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
  text-align: center;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.sidebar-ad-button:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.98);
}

.sidebar-ad-button-text {
  font-size: 20rpx;
  color: #FFFFFF;
  font-weight: bold;
}
</style>
