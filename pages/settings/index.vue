<template>
  <view class="container">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="back-btn" @click="goBack">
        <text class="back-icon">‹</text>
        <text class="back-text">返回</text>
      </view>
      <text class="title">联系客服</text>
      <view class="placeholder"></view>
    </view>

    <!-- 联系方式列表 -->
    <view class="contact-list">
      
      <!-- 联系客服 -->
      <view class="contact-section">
        <text class="section-title">联系我们</text>
        
        <view class="contact-item" @click="contactWeChat">
          <view class="contact-info">
            <text class="contact-icon">💬</text>
            <view class="contact-details">
              <text class="contact-title">微信客服</text>
              <text class="contact-desc">添加客服微信获得技术支持</text>
            </view>
          </view>
          <text class="arrow">›</text>
        </view>

        <view class="contact-item" @click="contactQQ">
          <view class="contact-info">
            <text class="contact-icon">💻</text>
            <view class="contact-details">
              <text class="contact-title">QQ群</text>
              <text class="contact-desc">加入用户交流群获得帮助</text>
            </view>
          </view>
          <text class="arrow">›</text>
        </view>

        <view class="contact-item" @click="contactEmail">
          <view class="contact-info">
            <text class="contact-icon">📧</text>
            <view class="contact-details">
              <text class="contact-title">邮箱反馈</text>
              <text class="contact-desc">发送邮件反馈问题或建议</text>
            </view>
          </view>
          <text class="arrow">›</text>
        </view>
      </view>

      <!-- 常见问题 -->
      <view class="contact-section">
        <text class="section-title">帮助中心</text>
        
        <view class="help-item" @click="showFAQ">
          <view class="help-info">
            <text class="help-icon">❓</text>
            <view class="help-details">
              <text class="help-title">常见问题</text>
              <text class="help-desc">查看使用说明和常见问题解答</text>
            </view>
          </view>
          <text class="arrow">›</text>
        </view>
      </view>

      <!-- 法律风险说明 -->
      <view class="contact-section">
        <text class="section-title">重要说明</text>
        
        <view class="legal-notice">
          <view class="notice-header">
            <text class="notice-icon">⚠️</text>
            <text class="notice-title">版权与免责声明</text>
          </view>
          <view class="notice-content">
            <text class="notice-item">1. 本工具仅能去除平台添加的水印，无法处理原始内容中的水印</text>
            <text class="notice-item">2. 解析的内容版权归原作者所有，请遵守相关法律法规</text>
            <text class="notice-item">3. 禁止用于商业用途或侵犯他人版权的行为</text>
            <text class="notice-item">4. 用户需自行承担使用本工具的法律责任</text>
            <text class="notice-item">5. 如有版权问题，请立即停止使用并删除相关内容</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      wechatId: '请添加微信号获取',
      qqGroup: '123456789',
      email: '<EMAIL>'
    }
  },
  
  methods: {
    goBack() {
      uni.navigateBack()
    },
    
    // 联系微信客服
    contactWeChat() {
      uni.showModal({
        title: '微信客服',
        content: `请添加微信客服：${this.wechatId}\n\n复制微信号到剪贴板后，打开微信添加好友`,
        confirmText: '复制微信号',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            uni.setClipboardData({
              data: this.wechatId,
              success: () => {
                uni.showToast({
                  title: '微信号已复制',
                  icon: 'success'
                })
              }
            })
          }
        }
      })
    },
    
    // 联系QQ群
    contactQQ() {
      uni.showModal({
        title: 'QQ交流群',
        content: `请加入QQ群：${this.qqGroup}\n\n复制群号到剪贴板后，打开QQ搜索加群`,
        confirmText: '复制群号',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            uni.setClipboardData({
              data: this.qqGroup,
              success: () => {
                uni.showToast({
                  title: 'QQ群号已复制',
                  icon: 'success'
                })
              }
            })
          }
        }
      })
    },
    
    // 邮箱反馈
    contactEmail() {
      uni.showModal({
        title: '邮箱反馈',
        content: `客服邮箱：${this.email}\n\n复制邮箱地址后，可发送邮件反馈问题`,
        confirmText: '复制邮箱',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            uni.setClipboardData({
              data: this.email,
              success: () => {
                uni.showToast({
                  title: '邮箱已复制',
                  icon: 'success'
                })
              }
            })
          }
        }
      })
    },
    
    // 显示常见问题
    showFAQ() {
      uni.showModal({
        title: '常见问题',
        content: '1. 为什么解析失败？\n请检查链接是否完整，是否为支持的平台\n\n2. 如何获得24小时解析权限？\n观看操作前的广告即可获得\n\n3. 解析的内容在哪里？\n保存到相册或通过历史记录查看',
        showCancel: false,
        confirmText: '知道了'
      })
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: #F5F7FA;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #E5E5E5;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.back-icon {
  font-size: 40rpx;
  color: #007AFF;
  font-weight: bold;
}

.back-text {
  font-size: 28rpx;
  color: #007AFF;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.placeholder {
  width: 120rpx;
}

.contact-list {
  padding: 20rpx;
}

.contact-section {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.contact-item,
.help-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #F8F8F8;
  display: flex;
  align-items: center;
  transition: background 0.2s ease;
}

.contact-item:last-child,
.help-item:last-child {
  border-bottom: none;
}

.contact-item:active,
.help-item:active {
  background: #F8F9FA;
}

.contact-info,
.help-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
}

.contact-icon,
.help-icon {
  font-size: 40rpx;
}

.contact-details,
.help-details {
  flex: 1;
}

.contact-title,
.help-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.contact-desc,
.help-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

.arrow {
  font-size: 32rpx;
  color: #C7C7CC;
}

.legal-notice {
  padding: 30rpx;
  background: #FFF9F9;
}

.notice-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.notice-icon {
  font-size: 28rpx;
}

.notice-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF6B35;
}

.notice-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.notice-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  position: relative;
  padding-left: 20rpx;
}

.notice-item:before {
  content: '•';
  position: absolute;
  left: 0;
  color: #FF6B35;
  font-weight: bold;
}
</style>