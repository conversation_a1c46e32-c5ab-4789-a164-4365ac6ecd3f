'use strict';

/**
 * 统一解析器入口 v1.0.0
 * 支持多平台内容解析的统一接口
 * 
 * 支持的平台：
 * - 抖音 (douyin.com, dy.com)
 * - 快手 (kuaishou.com, ks.com, v.kuaishou.com, kwai.app, ks.app, gifshow.com)
 * - 小红书 (xiaohongshu.com, xhslink.com)
 * - B站 (bilibili.com, b23.tv, b23.com)
 * - 微博 (video.weibo.com, weibo.com/tv, m.weibo.cn)
 * - 微视 (video.weishi.qq.com, weishi.qq.com)
 * - 皮皮虾 (h5.pipix.com, pipix.com)
 * - 汽水音乐 (qishui.douyin.com)
 */

// 平台配置 - 配置化管理，便于扩展
const PLATFORM_CONFIG = {
  douyin: {
    name: '抖音',
    parser: 'simple-douyin-parser',
    patterns: [
      /(?<!qishui\.)douyin\.com/i,  // 排除汽水音乐域名
      /dy\.com/i
    ],
    defaultOptions: {
      forceRemoveWatermark: true,
      debug: false
    }
  },
  kuaishou: {
    name: '快手', 
    parser: 'simple-kuaishou-parser',
    patterns: [
      /kuaishou\.com/i,
      /ks\.com/i,
      /v\.kuaishou\.com/i,
      /kwai\.app/i,
      /ks\.app/i,
      /gifshow\.com/i
    ],
    defaultOptions: {
      forceRemoveWatermark: true,
      debug: false
    }
  },
  xiaohongshu: {
    name: '小红书',
    parser: 'xiaohongshu-parser',
    patterns: [
      /xiaohongshu\.com/i,
      /xhslink\.com/i
    ],
    defaultOptions: {
      forceRemoveWatermark: false,
      debug: true
    }
  },
  bilibili: {
    name: 'B站',
    parser: 'bilibili-parser',
    patterns: [
      /bilibili\.com/i,
      /b23\.tv/i,
      /b23\.com/i
    ],
    defaultOptions: {
      forceRemoveWatermark: false,
      debug: false
    }
  },
  weibo: {
    name: '微博',
    parser: 'weibo-parser',
    patterns: [
      /video\.weibo\.com/i,
      /weibo\.com\/tv/i,
      /m\.weibo\.cn/i,
      /weibo\.com\/[^\/]+\/status/i,
      /weibo\.com\/\d+\/\d+/i,  // 支持 weibo.com/用户ID/微博ID 格式
      /weibo\.com\/u\/\d+/i,     // 支持 weibo.com/u/用户ID 格式
      /weibo\.com\/detail\/\d+/i // 支持 weibo.com/detail/微博ID 格式
    ],
    defaultOptions: {
      forceRemoveWatermark: false,
      debug: true  // 开启调试模式，便于测试图文解析
    }
  },
  weishi: {
    name: '微视',
    parser: 'weishi-parser',
    patterns: [
      /video\.weishi\.qq\.com/i,
      /weishi\.qq\.com/i
    ],
    defaultOptions: {
      forceRemoveWatermark: false,
      debug: true  // 开启调试模式，便于查看解析结果
    }
  },
  pipix: {
    name: '皮皮虾',
    parser: 'pipix-parser',
    patterns: [
      /h5\.pipix\.com/i,
      /pipix\.com/i
    ],
    defaultOptions: {
      forceRemoveWatermark: false,
      debug: true  // 开启调试模式，便于查看解析结果
    }
  },
  qishui: {
    name: '汽水音乐',
    parser: 'qishui-music-parser',
    patterns: [
      /qishui\.douyin\.com/i
    ],
    defaultOptions: {
      forceRemoveWatermark: false,
      debug: true  // 开启调试模式，便于查看解析结果
    }
  }
};

/**
 * 验证链接有效性
 * @param {string} link - 分享链接
 * @returns {boolean} - 是否有效
 */
function isValidLink(link) {
  if (!link || typeof link !== 'string') {
    return false;
  }
  
  // 使用所有平台的正则表达式进行验证
  for (const [platform, config] of Object.entries(PLATFORM_CONFIG)) {
    if (config.patterns.some(pattern => pattern.test(link))) {
      return true;
    }
  }
  
  return false;
}

/**
 * 检测平台类型
 * @param {string} link - 分享链接
 * @returns {string|null} - 平台标识或null
 */
function detectPlatform(link) {
  if (!link || typeof link !== 'string') {
    return null;
  }
  
  for (const [platform, config] of Object.entries(PLATFORM_CONFIG)) {
    if (config.patterns.some(pattern => pattern.test(link))) {
      return platform;
    }
  }
  
  return null;
}

/**
 * 清理链接 - 提取主要URL
 * @param {string} text - 包含链接的文本
 * @returns {string} - 清理后的链接
 */
function cleanLink(text) {
  if (!text || typeof text !== 'string') {
    return '';
  }
  
  // 移除多余的空格和换行
  text = text.trim().replace(/\s+/g, ' ');
  
  // 提取URL部分
  const urlMatch = text.match(/(https?:\/\/[^\s]+)/);
  if (urlMatch) {
    return urlMatch[1];
  }
  
  // 如果没有找到完整URL，尝试提取域名相关部分
  const domainMatch = text.match(/([a-zA-Z0-9.-]+\.(com|cn|net|org)\/[^\s]*)/);
  if (domainMatch) {
    return 'https://' + domainMatch[1];
  }
  
  return text.trim();
}

/**
 * 标准化返回结果 - 确保各平台返回格式统一
 * @param {Object} result - 解析器返回的结果
 * @param {string} platform - 平台标识
 * @param {string} originalLink - 原始链接
 * @returns {Object} - 标准化后的结果
 */
function standardizeResult(result, platform, originalLink) {
  console.log('standardizeResult 输入:', { 
    hasResult: !!result, 
    hasResultResult: !!result?.result,
    hasResultResultSuccess: !!result?.result?.success,
    hasResultResultData: !!result?.result?.data,
    platform, 
    originalLink 
  });
  
  // 添加微博数据检查
  if (result?.data && platform === 'weibo') {
    console.log('🔍 unified-parser接收微博数据:', {
      images长度: Array.isArray(result.data.images) ? result.data.images.length : 'N/A',
      imageUrls长度: Array.isArray(result.data.imageUrls) ? result.data.imageUrls.length : 'N/A',
      第一个imageUrl类型: result.data.imageUrls?.[0] ? typeof result.data.imageUrls[0] : 'N/A',
      第一个imageUrl示例: result.data.imageUrls?.[0],
      videoUrls长度: Array.isArray(result.data.videoUrls) ? result.data.videoUrls.length : 'N/A'
    });
  }

  // 添加微视数据检查
  if (result?.data && platform === 'weishi') {
    console.log('🎬 unified-parser接收微视数据:', {
      标题: result.data.title,
      作者: result.data.author,
      视频URL: result.data.videoUrl ? '✅ 已获取' : '❌ 缺失',
      封面URL: result.data.coverUrl ? '✅ 已获取' : '❌ 缺失',
      时长: result.data.duration + 'ms',
      尺寸: `${result.data.width}x${result.data.height}`,
      点赞数: result.data.likeCount,
      播放数: result.data.playCount,
      解析方法: result.data.method,
      视频规格数量: result.data.videoSpecUrls ? Object.keys(result.data.videoSpecUrls).length : 0
    });
  }

  // 处理云函数调用失败的情况
  if (!result) {
    throw new Error(`${PLATFORM_CONFIG[platform]?.name || '未知平台'}解析器无响应`);
  }

  // 处理不同的返回格式
  let data = null;
  
  // 快手解析器直接返回数据对象，包装在 {success: true, data: {...}} 中
  if (result.result && result.result.success && result.result.data) {
    data = result.result.data;
  }
  // 其他解析器可能直接返回结果对象
  else if (result.result && !result.result.success && !result.result.data) {
    // 直接返回的结果对象 (小红书、抖音)
    data = result.result;
  }
  // 处理错误情况
  else {
    const errorMsg = result.result?.message || 
                    result.result?.error?.message || 
                    `${PLATFORM_CONFIG[platform]?.name || '未知平台'}解析失败`;
    throw new Error(errorMsg);
  }
  
  const platformName = PLATFORM_CONFIG[platform]?.name || '未知平台';
  
  // 标准化字段，确保结果页面需要的字段都存在
  const standardizedResult = {
    // 基本信息
    title: data.title,
    author: data.author || '未知作者', 
    content: data.content || '',
    
    // 媒体内容
    processedData: data.processedData || null,
    
    // 类型和平台信息
    type: data.type || 'unknown',
    platform: platform,
    source: platformName,
    
    // 链接信息
    originalUrl: originalLink,
    originalLink: originalLink, // 保持兼容性
    
    // 附加信息
    coverUrl: data.coverUrl || null,
    note: data.note || null,
    timestamp: Date.now(),
    version: "统一解析器 v1.0.0",
    
    // 调试信息 (可选)
    debug: data.debug || null
  };

  // 若下游未提供 processedData，这里根据常见字段自动构造（优先适配微博 H5 直链）
  if (!standardizedResult.processedData) {
    const videoUrl = data.videoUrl || null;
    const videoUrls = Array.isArray(data.videoUrls) ? data.videoUrls : null;
    const images = Array.isArray(data.images) ? data.images : null;
    const imageUrls = Array.isArray(data.imageUrls) ? data.imageUrls : null;
    const livePhotos = Array.isArray(data.livePhotos) ? data.livePhotos : null;
    const livePhotoVideos = Array.isArray(data.livePhotoVideos) ? data.livePhotoVideos : null;
    
    if (videoUrl) {
      standardizedResult.processedData = {
        isUrl: true,
        data: videoUrl,
        type: 'video/mp4',
        duration: data.duration || 0,
        isLongVideo: !!data.isLongVideo,
        qualityUrls: data.qualityUrls || null,
        videoUrls: videoUrls || null,
        isH5Friendly: !!data.isH5Friendly,
        requiresProxy: false
      };
      standardizedResult.type = 'video';
    } else if (images && images.length > 0) {
      // 优先处理图文内容（即使有Live Photo视频）
      // 处理图文内容
      standardizedResult.processedData = {
        isUrl: false,
        data: null,
        type: 'image',
        images: images,
        imageUrls: imageUrls && imageUrls.length > 0 ? imageUrls : (images ? images.map((img, index) => {
          if (typeof img === 'string') return img;
          if (img && typeof img === 'object') {
            const url = img.url || img.imageUrl || img.src || img.large?.url || img.original?.url || '';
            if (platform === 'weibo' && index === 0) {
              console.log('🔄 unified-parser图片映射:', {
                原对象键值: Object.keys(img),
                找到的URL: url
              });
            }
            return url;
          }
          return String(img); // 安全转换
        }).filter(url => url && url !== '') : []),
        livePhotos: livePhotos || [],
        livePhotoVideos: (() => {
          // 优先使用解析器提供的livePhotoVideos
          if (livePhotoVideos && Array.isArray(livePhotoVideos)) {
            return livePhotoVideos;
          }

          // 若已提供videoUrls且长度与images相等，直接返回
          if (videoUrls && Array.isArray(videoUrls) && videoUrls.length === images.length) {
            return videoUrls;
          }

          // 否则尝试根据 livePhotos 数组进行对齐
          const aligned = Array(images.length).fill(null);

          const extractImageUrl = (img) => {
            if (!img) return '';
            if (typeof img === 'string') return img;
            if (typeof img === 'object') {
              return img.url || img.imageUrl || img.src || img.large?.url || img.original?.url || '';
            }
            return String(img);
          };

          // 预先构建 imageUrl 映射表，提高匹配效率
          const imageUrlMap = images.map(extractImageUrl);

          if (Array.isArray(livePhotos) && livePhotos.length > 0) {
            livePhotos.forEach(lp => {
              if (!lp || typeof lp !== 'object' || !lp.videoUrl) return;

              // 1) index 字段优先
              if (lp.index !== undefined && lp.index >= 0 && lp.index < aligned.length) {
                aligned[lp.index] = lp.videoUrl;
                return;
              }

              // 2) imageUrl 匹配
              if (lp.imageUrl) {
                const idx = imageUrlMap.findIndex(u => u === lp.imageUrl);
                if (idx !== -1) {
                  aligned[idx] = lp.videoUrl;
                }
              }
            });
          }

          // 若仍无任何匹配，回退为 videoUrls 或空数组
          const anyMatch = aligned.some(v => v !== null);
          return anyMatch ? aligned : (videoUrls || []);
        })(), // Live Photo 视频URLs（按图片索引对齐）
        count: images.length
      };
      standardizedResult.type = 'image';
      
      // 调试：检查processedData.imageUrls
      if (platform === 'weibo') {
        console.log('🔍 unified-parser设置processedData.imageUrls:', {
          使用了现有imageUrls: !!(imageUrls && imageUrls.length > 0),
          最终imageUrls长度: standardizedResult.processedData.imageUrls.length,
          第一个URL类型: typeof standardizedResult.processedData.imageUrls[0],
          第一个URL示例: standardizedResult.processedData.imageUrls[0]
        });
      }
      
      // 添加图文相关字段到根级别（兼容前端）
      standardizedResult.images = images;
      standardizedResult.imageUrls = imageUrls && imageUrls.length > 0 ? imageUrls : (images ? images.map(img => {
        if (typeof img === 'string') return img;
        if (img && typeof img === 'object') {
          return img.url || img.imageUrl || img.src || img.large?.url || img.original?.url || '';
        }
        return String(img); // 安全转换
      }).filter(url => url && url !== '') : []);
      standardizedResult.livePhotos = livePhotos || [];
      standardizedResult.livePhotoVideos = standardizedResult.processedData.livePhotoVideos;
    } else if (videoUrls && videoUrls.length > 0) {
      // 处理纯视频内容（没有图片时）
      standardizedResult.processedData = {
        isUrl: true,
        data: videoUrls[0],
        type: 'video/mp4',
        videoUrls,
        isH5Friendly: !!data.isH5Friendly,
        requiresProxy: false
      };
      standardizedResult.type = 'video';
    }
  }

  console.log('标准化结果:', {
    title: standardizedResult.title,
    platform: standardizedResult.platform,
    type: standardizedResult.type,
    hasProcessedData: !!standardizedResult.processedData
  });
  
  return standardizedResult;
}

/**
 * 云函数入口
 */
exports.main = async (event, context) => {
  const startTime = Date.now();
  console.log('=== unified-parser 开始处理 ===');
  console.log('请求参数:', {
    link: event.link,
    options: event.options,
    timestamp: new Date().toISOString()
  });
  
  try {
    // 参数验证
    if (!event.link) {
      throw new Error('缺少必要参数: link');
    }
    
    // 清理链接
    const cleanedLink = cleanLink(event.link);
    if (!cleanedLink) {
      throw new Error('链接格式无效');
    }
    
    console.log('清理后的链接:', cleanedLink);
    
    // 验证链接有效性
    if (!isValidLink(cleanedLink)) {
      throw new Error('不支持的链接格式，请检查链接是否正确');
    }
    
    // 检测平台
    const platform = detectPlatform(cleanedLink);
    if (!platform) {
      throw new Error('不支持的平台，当前支持：抖音、快手、小红书、B站、微博、微视');
    }
    
    const platformConfig = PLATFORM_CONFIG[platform];
    console.log('检测到平台:', platform, '-> 解析器:', platformConfig.parser);
    
    // 准备调用参数 - 合并默认配置和用户选项
    const callParams = {
      link: cleanedLink,
      ...platformConfig.defaultOptions,
      ...(event.options || {})
    };
    
    console.log('调用解析器参数:', callParams);
    
    // 调用对应的平台解析器
    const result = await uniCloud.callFunction({
      name: platformConfig.parser,
      data: callParams
    });
    
    const endTime = Date.now();
    console.log('解析器调用完成:', {
      platform: platform,
      parser: platformConfig.parser,
      duration: endTime - startTime + 'ms',
      hasResult: !!result,
      hasResultResult: !!result?.result
    });
    
    // 标准化返回结果
    const standardizedResult = standardizeResult(result, platform, event.link);
    
    console.log('=== unified-parser 处理完成 ===');
    return standardizedResult;
    
  } catch (error) {
    const endTime = Date.now();
    console.error('=== unified-parser 处理失败 ===');
    console.error('错误详情:', {
      message: error.message,
      duration: endTime - startTime + 'ms',
      stack: error.stack
    });
    
    // 统一错误返回格式
    return {
      title: '解析失败',
      author: '系统提示', 
      content: error.message,
      processedData: null,
      type: 'error',
      platform: 'error',
      source: '系统错误',
      originalUrl: event.link || '',
      originalLink: event.link || '',
      timestamp: Date.now(),
      version: "统一解析器 v1.0.0",
      error: {
        message: error.message,
        code: 'PARSE_ERROR',
        timestamp: new Date().toISOString()
      }
    };
  }
};