'use strict';

/**
 * 文件下载云函数
 * 处理视频和封面的下载，避免小程序直接请求外部域名的限制
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

exports.main = async (event, context) => {
  console.log('文件下载云函数被调用，参数:', event);

  const { action, url, options = {} } = event;

  try {
    switch (action) {
      case 'downloadFile':
        return await downloadFile(url, options);
      case 'testUrl':
        return await testUrl(url);
      case 'getFileInfo':
        return await getFileInfo(url);
      default:
        throw new Error('不支持的操作类型');
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return {
      success: false,
      error: error.message,
      code: error.code || 'UNKNOWN_ERROR'
    };
  }
};

/**
 * 下载文件并返回base64数据
 */
async function downloadFile(url, options = {}, redirectCount = 0) {
  console.log('开始下载文件:', url);
  
  if (!url || typeof url !== 'string') {
    throw new Error('无效的URL参数');
  }

  // 防止无限重定向
  const maxRedirects = 5;
  if (redirectCount > maxRedirects) {
    throw new Error('重定向次数过多，可能存在循环重定向');
  }

  const { maxSize = 200 * 1024 * 1024, timeout = 300000 } = options; // 默认200MB，5分钟超时

  try {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const requestModule = isHttps ? https : http;

    // 构建请求头
    const headers = getRequestHeaders(url);

    return new Promise((resolve, reject) => {
      const request = requestModule.get(url, {
        headers,
        timeout: timeout
      }, (response) => {
        console.log('响应状态码:', response.statusCode);
        console.log('响应头:', response.headers);

        // 处理重定向
        if (response.statusCode === 301 || response.statusCode === 302 || response.statusCode === 303 || response.statusCode === 307 || response.statusCode === 308) {
          const redirectUrl = response.headers.location;
          if (!redirectUrl) {
            reject(new Error(`收到重定向响应但无location头，状态码: ${response.statusCode}`));
            return;
          }
          
          console.log(`检测到重定向 (${response.statusCode}):`, redirectUrl);
          
          // 处理相对URL
          let finalRedirectUrl;
          if (redirectUrl.startsWith('http')) {
            finalRedirectUrl = redirectUrl;
          } else {
            // 相对URL，需要拼接
            const baseUrl = `${urlObj.protocol}//${urlObj.host}`;
            finalRedirectUrl = new URL(redirectUrl, baseUrl).href;
          }
          
          console.log('重定向到:', finalRedirectUrl);
          
          // 递归调用下载函数处理重定向
          downloadFile(finalRedirectUrl, options, redirectCount + 1)
            .then(resolve)
            .catch(reject);
          return;
        }

        // 检查响应状态
        if (response.statusCode !== 200) {
          reject(new Error(`下载失败，状态码: ${response.statusCode}`));
          return;
        }

        // 检查内容类型
        const contentType = response.headers['content-type'] || '';
        if (contentType.includes('text/html') || contentType.includes('application/json')) {
          reject(new Error('服务器返回的不是文件内容'));
          return;
        }

        // 检查文件大小
        const contentLength = parseInt(response.headers['content-length'] || '0');
        if (contentLength > maxSize) {
          reject(new Error(`文件过大（${Math.round(contentLength / 1024 / 1024)}MB），超过限制`));
          return;
        }

        const chunks = [];
        let receivedBytes = 0;

        response.on('data', (chunk) => {
          chunks.push(chunk);
          receivedBytes += chunk.length;

          // 实时检查大小限制
          if (receivedBytes > maxSize) {
            request.destroy();
            reject(new Error('下载过程中文件大小超过限制'));
            return;
          }
        });

        response.on('end', () => {
          try {
            const buffer = Buffer.concat(chunks);
            const base64Data = buffer.toString('base64');
            
            console.log('下载完成，文件大小:', receivedBytes, 'bytes');
            
            resolve({
              success: true,
              data: base64Data,
              contentType: contentType,
              size: receivedBytes,
              filename: getFilenameFromUrl(url, contentType)
            });
          } catch (error) {
            reject(new Error('处理下载数据失败: ' + error.message));
          }
        });

        response.on('error', (error) => {
          reject(new Error('下载过程中出错: ' + error.message));
        });
      });

      request.on('timeout', () => {
        request.destroy();
        reject(new Error('下载超时'));
      });

      request.on('error', (error) => {
        reject(new Error('请求失败: ' + error.message));
      });
    });

  } catch (error) {
    throw new Error('下载文件失败: ' + error.message);
  }
}

/**
 * 测试URL是否可访问
 */
async function testUrl(url, redirectCount = 0) {
  console.log('测试URL可访问性:', url);
  
  // 防止无限重定向
  const maxRedirects = 5;
  if (redirectCount > maxRedirects) {
    throw new Error('测试URL时重定向次数过多');
  }
  
  try {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const requestModule = isHttps ? https : http;

    const headers = getRequestHeaders(url);
    
    // 🔧 对微博纯视频URL使用GET请求而不是HEAD请求（微博CDN拒绝HEAD但允许GET）
    const isWeiboVideo = (url.includes('weibocdn.com') || url.includes('sinaimg.cn')) && 
                        !url.includes('livephoto=') && 
                        (url.includes('.mp4') || url.includes('.mov'));
    const method = isWeiboVideo ? 'GET' : 'HEAD';
    
    console.log(`使用${method}方法测试URL:`, url);

    return new Promise((resolve, reject) => {
      const request = requestModule.request({
        method: method,
        hostname: urlObj.hostname,
        port: urlObj.port,
        path: urlObj.pathname + urlObj.search,
        headers,
        timeout: 10000
      }, (response) => {
        console.log('URL测试响应状态码:', response.statusCode);
        
        // 处理重定向
        if (response.statusCode === 301 || response.statusCode === 302 || response.statusCode === 303 || response.statusCode === 307 || response.statusCode === 308) {
          const redirectUrl = response.headers.location;
          if (!redirectUrl) {
            reject(new Error(`测试URL时收到重定向响应但无location头，状态码: ${response.statusCode}`));
            return;
          }
          
          console.log(`测试URL时检测到重定向 (${response.statusCode}):`, redirectUrl);
          
          // 处理相对URL
          let finalRedirectUrl;
          if (redirectUrl.startsWith('http')) {
            finalRedirectUrl = redirectUrl;
          } else {
            const baseUrl = `${urlObj.protocol}//${urlObj.host}`;
            finalRedirectUrl = new URL(redirectUrl, baseUrl).href;
          }
          
          console.log('测试URL重定向到:', finalRedirectUrl);
          
          // 递归调用处理重定向
          testUrl(finalRedirectUrl, redirectCount + 1)
            .then(resolve)
            .catch(reject);
          return;
        }
        
        if (response.statusCode === 200 || response.statusCode === 206) {
          // 如果是GET请求，需要消耗响应数据以避免内存泄漏
          if (method === 'GET') {
            response.on('data', () => {});
            response.on('end', () => {});
          }
          
          resolve({
            success: true,
            statusCode: response.statusCode,
            contentLength: response.headers['content-length'],
            contentType: response.headers['content-type']
          });
        } else {
          reject(new Error(`URL不可访问，状态码: ${response.statusCode}`));
        }
      });

      request.on('timeout', () => {
        request.destroy();
        reject(new Error('URL测试超时'));
      });

      request.on('error', (error) => {
        reject(new Error('URL测试失败: ' + error.message));
      });

      request.end();
    });

  } catch (error) {
    throw new Error('URL测试失败: ' + error.message);
  }
}

/**
 * 获取文件信息（不下载文件内容）
 */
async function getFileInfo(url) {
  console.log('获取文件信息:', url);
  
  try {
    const result = await testUrl(url);
    return {
      success: true,
      size: parseInt(result.contentLength || '0'),
      contentType: result.contentType,
      statusCode: result.statusCode
    };
  } catch (error) {
    throw new Error('获取文件信息失败: ' + error.message);
  }
}

/**
 * 根据URL来源构建请求头
 */
function getRequestHeaders(url) {
  const headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
  };

  // 根据URL来源设置合适的Referer
  if (url.includes('douyin') || url.includes('aweme')) {
    headers['Referer'] = 'https://www.douyin.com/';
  } else if (url.includes('xiaohongshu')) {
    headers['Referer'] = 'https://www.xiaohongshu.com/';
  } else if (url.includes('bilibili') || url.includes('bilivideo')) {
    headers['Referer'] = 'https://www.bilibili.com/';
    headers['Origin'] = 'https://www.bilibili.com';
  } else if (url.includes('weibocdn.com') || url.includes('sinaimg.cn') || url.includes('video.weibo.com')) {
    headers['Referer'] = 'https://weibo.com/';
    headers['Accept'] = '*/*';
    headers['Accept-Language'] = 'zh-CN,zh;q=0.9,en;q=0.8';
    headers['Cache-Control'] = 'no-cache';
    headers['Pragma'] = 'no-cache';
    // 对于Live Photo视频，添加额外的头信息
    if (url.includes('livephoto=') || url.includes('video.weibo.com')) {
      headers['Accept-Encoding'] = 'gzip, deflate, br';
      headers['Connection'] = 'keep-alive';
      headers['Sec-Fetch-Dest'] = 'video';
      headers['Sec-Fetch-Mode'] = 'no-cors';
      headers['Sec-Fetch-Site'] = 'cross-site';
    }
  } else if (url.includes('kuaishou') || url.includes('ks.com')) {
    headers['Referer'] = 'https://www.kuaishou.com/';
  } else if (url.includes('weishi.qq.com') || url.includes('video.weishi.qq.com')) {
    headers['Referer'] = 'https://weishi.qq.com/';
    headers['Accept'] = '*/*';
    headers['Accept-Language'] = 'zh-CN,zh;q=0.9,en;q=0.8';
  }

  return headers;
}

/**
 * 从URL和内容类型推断文件名
 */
function getFilenameFromUrl(url, contentType) {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    
    // 尝试从URL路径获取文件名
    const segments = pathname.split('/');
    let filename = segments[segments.length - 1];
    
    if (!filename || !filename.includes('.')) {
      // 根据内容类型生成文件名
      if (contentType.includes('video')) {
        filename = `video_${Date.now()}.mp4`;
      } else if (contentType.includes('image')) {
        filename = `image_${Date.now()}.jpg`;
      } else {
        filename = `file_${Date.now()}`;
      }
    }
    
    return filename;
  } catch (error) {
    return `file_${Date.now()}`;
  }
}